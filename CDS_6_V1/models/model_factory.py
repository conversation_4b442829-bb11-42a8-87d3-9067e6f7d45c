"""
Model factory for creating LLM and embedding models from different providers.
"""
import os
from typing import Union, Any

from huggingface_hub import login
from langchain_core.language_models import BaseChatModel
from langchain_core.embeddings import Embeddings
from langchain_huggingface import ChatHuggingFace, HuggingFacePipeline
from config import Settings

os.environ["PYTORCH_MPS_HIGH_WATERMARK_RATIO"] = "0.0"


class ModelFactory:
    """Factory class for creating models from different providers."""
    
    @staticmethod
    def create_llm(settings: Settings) -> BaseChatModel:
        """Create LLM based on the configured provider."""
        if settings.model_provider.lower() == "ollama":
            return ModelFactory._create_ollama_llm(settings)
        elif settings.model_provider.lower() == "huggingface":
            return ModelFactory._create_huggingface_llm(settings)
        else:
            raise ValueError(f"Unsupported model provider: {settings.model_provider}")
    
    @staticmethod
    def create_embeddings(settings: Settings) -> Embeddings:
        """Create embeddings based on the configured provider."""
        if settings.embedding_provider.lower() == "ollama":
            return ModelFactory._create_ollama_embeddings(settings)
        elif settings.embedding_provider.lower() == "huggingface":
            return ModelFactory._create_huggingface_embeddings(settings)
        else:
            raise ValueError(f"Unsupported embedding provider: {settings.model_provider}")
    
    @staticmethod
    def _create_ollama_llm(settings: Settings) -> BaseChatModel:
        """Create Ollama LLM."""
        try:
            from langchain_ollama import ChatOllama
            
            llm = ChatOllama(
                model=settings.ollama_model_name,
                base_url=settings.ollama_base_url,
                temperature=0.0,
                extract_reasoning=False,
                num_predict=-1,
                top_k=5,
                top_p=0.3,
                format="json",
                think=False
            )
            print(f"✅ Ollama LLM initialized: {settings.ollama_model_name}")
            return llm
        except ImportError:
            raise ImportError("langchain_ollama is required for Ollama models. Install with: pip install langchain-ollama")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Ollama LLM: {e}")
    
    @staticmethod
    def _create_ollama_embeddings(settings: Settings) -> Embeddings:
        """Create Ollama embeddings."""
        try:
            from langchain_ollama.embeddings import OllamaEmbeddings
            
            embeddings = OllamaEmbeddings(
                model=settings.ollama_embedding_model,
                base_url=settings.ollama_base_url
            )
            print(f"✅ Ollama embeddings initialized: {settings.ollama_embedding_model}")
            return embeddings
        except ImportError:
            raise ImportError("langchain_ollama is required for Ollama embeddings. Install with: pip install langchain-ollama")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Ollama embeddings: {e}")
    
    @staticmethod
    def _create_huggingface_llm(settings: Settings) -> BaseChatModel:
        """Create Hugging Face LLM."""
        try:
            from langchain_huggingface import ChatHuggingFace, HuggingFacePipeline
            from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
            import torch

            # Get token from environment
            token = os.getenv("HUGGINGFACE_API_TOKEN")
            if token:
                login(token)
                print(f"✅ Logged in to Hugging Face with token")
            else:
                print("⚠️  No Hugging Face token found, using public models only")

            # Validate model name
            print(f"🔍 Debug - settings.hf_model_name: '{settings.hf_model_name}'")
            print(f"🔍 Debug - type: {type(settings.hf_model_name)}")

            if not settings.hf_model_name or settings.hf_model_name.strip() == "":
                raise ValueError("HF_MODEL_NAME is not set or is empty")

            print(f"🔄 Initializing Hugging Face model: {settings.hf_model_name}")

            # Try a simpler approach first - use transformers pipeline directly
            try:
                # Create pipeline with explicit configuration
                pipe = pipeline(
                    "text-generation",
                    model=settings.hf_model_name,
                    tokenizer=settings.hf_model_name,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                    max_new_tokens=512,
                    do_sample=True,
                    temperature=0.1,
                    return_full_text=False,
                    pad_token_id=50256  # Common pad token ID
                )

                # Create HuggingFacePipeline from the pipeline
                hf_llm = HuggingFacePipeline(pipeline=pipe)

            except Exception as pipeline_error:
                print(f"⚠️  Pipeline creation failed: {pipeline_error}")
                print("🔄 Trying alternative approach...")

                # Fallback: Use from_model_id with minimal configuration
                hf_llm = HuggingFacePipeline.from_model_id(
                    model_id=settings.hf_model_name,
                    task="text-generation",
                    device=0 if torch.cuda.is_available() else -1,
                    model_kwargs={
                        "torch_dtype": torch.float16 if torch.cuda.is_available() else torch.float32,
                        "low_cpu_mem_usage": True,
                    },
                    pipeline_kwargs={
                        "max_new_tokens": 512,
                        "do_sample": True,
                        "temperature": 0.1,
                        "return_full_text": False,
                    }
                )

            # Wrap in ChatHuggingFace for chat interface
            llm = ChatHuggingFace(llm=hf_llm, verbose=True)

            print(f"✅ Hugging Face LLM initialized: {settings.hf_model_name}")
            return llm

        except ImportError:
            raise ImportError("langchain_huggingface and transformers are required for HF models. Install with: pip install langchain-huggingface transformers")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Hugging Face LLM: {e}")
    
    @staticmethod
    def _create_huggingface_embeddings(settings: Settings) -> Embeddings:
        """Create Hugging Face embeddings."""
        try:
            from langchain_huggingface import HuggingFaceEmbeddings
            
            embeddings = HuggingFaceEmbeddings(
                model_name=settings.hf_embedding_model,
                model_kwargs={'device': 'cpu'},  # Change to 'cuda' if GPU available
                encode_kwargs={'normalize_embeddings': True}
            )
            print(f"✅ Hugging Face embeddings initialized: {settings.hf_embedding_model}")
            return embeddings
        except ImportError:
            raise ImportError("langchain_huggingface is required for HF embeddings. Install with: pip install langchain-huggingface")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Hugging Face embeddings: {e}")


def get_models(settings: Settings) -> tuple[BaseChatModel, Embeddings]:
    """Get both LLM and embeddings models."""
    llm = ModelFactory.create_llm(settings)
    embeddings = ModelFactory.create_embeddings(settings)
    return llm, embeddings
